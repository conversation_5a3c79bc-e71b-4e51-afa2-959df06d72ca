"""
Entity extraction service for the Graphiti application.

This service handles entity extraction from text and documents.
"""

import json
from typing import List, Dict, Any
from datetime import datetime

from utils.logging_utils import get_logger
from database.database_service import get_falkordb_adapter, create_entity_node, link_entity_to_fact

# Import entity extraction
from entity_extraction import extract_entities_from_text as extract_entities

# Set up logger
logger = get_logger(__name__)


async def extract_entities_from_text(text: str, document_id: str = None, fact_id: str = None, llm_provider: str = 'openrouter') -> List[Dict[str, Any]]:
    """
    Extract entities from text.

    Args:
        text: Text to extract entities from
        document_id: Optional document ID
        fact_id: Optional fact ID
        llm_provider: LLM provider to use

    Returns:
        List of extracted entities
    """
    logger.info(f"Extracting entities from text (length: {len(text)})")

    try:
        # Extract entities
        entities = await extract_entities(text, llm_provider)

        # If entities is a string, try to parse it as JSON
        if isinstance(entities, str):
            try:
                entities = json.loads(entities)
            except json.JSONDecodeError:
                logger.error(f"Error parsing entities JSON: {entities}")
                return []

        # If entities is a dict, extract the entities list
        if isinstance(entities, dict) and "entities" in entities:
            entities = entities["entities"]

        # Create entity nodes and link to fact if provided
        if fact_id:
            for entity in entities:
                # Create entity node
                entity_uuid = await create_entity_node(
                    name=entity.get("name", "Unknown"),
                    entity_type=entity.get("type", "Other"),
                    properties={
                        "document_id": document_id,
                        "confidence": entity.get("confidence", 1.0),
                        "created_at": datetime.now().isoformat()
                    }
                )

                # Link entity to fact
                if entity_uuid:
                    await link_entity_to_fact(entity_uuid, fact_id, {
                        "confidence": entity.get("confidence", 1.0)
                    })

        return entities

    except Exception as e:
        logger.error(f"Error extracting entities: {e}")
        return []


async def extract_entities_from_document(document_id: str, llm_provider: str = 'openrouter') -> Dict[str, Any]:
    """
    Extract entities from a document.

    Args:
        document_id: Document ID
        llm_provider: LLM provider to use

    Returns:
        Extraction result
    """
    logger.info(f"Extracting entities from document {document_id}")

    try:
        adapter = await get_falkordb_adapter()

        # Get facts for the document
        query = f"""
        MATCH (e:Episode {{uuid: '{document_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f.uuid as uuid, f.body as body
        """

        result = adapter.execute_cypher(query)

        if not result or len(result) <= 1 or len(result[1]) == 0:
            logger.error(f"No facts found for document {document_id}")
            return {
                "document_id": document_id,
                "success": False,
                "error": "No facts found for document",
                "entities_extracted": 0
            }

        # Extract entities from each fact
        total_entities = 0
        for row in result[1]:
            fact_id = row[0]
            body = row[1]

            # Extract entities
            entities = await extract_entities_from_text(body, document_id, fact_id, llm_provider)
            total_entities += len(entities)

        return {
            "document_id": document_id,
            "success": True,
            "entities_extracted": total_entities
        }

    except Exception as e:
        logger.error(f"Error extracting entities from document: {e}")
        return {
            "document_id": document_id,
            "success": False,
            "error": str(e),
            "entities_extracted": 0
        }
