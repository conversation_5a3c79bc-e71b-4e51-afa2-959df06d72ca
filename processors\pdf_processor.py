"""
Enhanced PDF Processor
Handles PDF files with improved Mistral OCR integration and fallback methods.
"""

import os
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class PDFProcessor:
    """
    Enhanced processor for PDF documents with multiple extraction methods.
    """
    
    def __init__(self):
        """Initialize the PDF processor."""
        self.supported_extensions = ['.pdf']
        
        # Initialize available extraction methods
        self.mistral_ocr = self._initialize_mistral_ocr()
        self.pypdf2_available = self._check_pypdf2_availability()
        self.pymupdf_available = self._check_pymupdf_availability()
    
    def _initialize_mistral_ocr(self):
        """Initialize Mistral OCR if available."""
        try:
            from utils.mistral_ocr import MistralOCRProcessor
            return MistralOCRProcessor()
        except Exception as e:
            logger.warning(f"Mistral OCR not available: {e}")
            return None
    
    def _check_pypdf2_availability(self) -> bool:
        """Check if PyPDF2 is available."""
        try:
            import PyPDF2
            return True
        except ImportError:
            logger.warning("PyPDF2 not available. Install with: pip install PyPDF2")
            return False
    
    def _check_pymupdf_availability(self) -> bool:
        """Check if PyMuPDF is available."""
        try:
            import fitz  # PyMuPDF
            return True
        except ImportError:
            logger.warning("PyMuPDF not available. Install with: pip install PyMuPDF")
            return False
    
    async def extract_text(self, file_path: Path, max_pages: Optional[int] = None) -> Dict[str, Any]:
        """
        Extract text from a PDF file using the best available method.
        
        Args:
            file_path: Path to the PDF file
            max_pages: Maximum number of pages to process
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            if not file_path.suffix.lower() == '.pdf':
                return {
                    'success': False,
                    'error': f"Not a PDF file: {file_path}",
                    'text': '',
                    'metadata': {}
                }
            
            # Try Mistral OCR first (best quality)
            if self.mistral_ocr:
                try:
                    logger.info(f"Extracting text from {file_path} using Mistral OCR")
                    text = await self.mistral_ocr.extract_text_from_pdf(str(file_path), max_pages)
                    
                    if text and len(text.strip()) > 0:
                        metadata = await self._extract_metadata_mistral(file_path, max_pages)
                        return {
                            'success': True,
                            'text': text.strip(),
                            'metadata': metadata,
                            'ocr_provider': 'mistral-ocr',
                            'extraction_method': 'mistral_ocr'
                        }
                    else:
                        logger.warning(f"Mistral OCR returned empty text for {file_path}")
                except Exception as e:
                    logger.warning(f"Mistral OCR failed for {file_path}: {e}")
            
            # Fallback to PyMuPDF
            if self.pymupdf_available:
                try:
                    logger.info(f"Extracting text from {file_path} using PyMuPDF")
                    result = await self._extract_with_pymupdf(file_path, max_pages)
                    
                    if result['success'] and result['text'].strip():
                        return result
                except Exception as e:
                    logger.warning(f"PyMuPDF extraction failed for {file_path}: {e}")
            
            # Fallback to PyPDF2
            if self.pypdf2_available:
                try:
                    logger.info(f"Extracting text from {file_path} using PyPDF2")
                    result = await self._extract_with_pypdf2(file_path, max_pages)
                    
                    if result['success'] and result['text'].strip():
                        return result
                except Exception as e:
                    logger.warning(f"PyPDF2 extraction failed for {file_path}: {e}")
            
            # If all methods fail
            return {
                'success': False,
                'error': f"Failed to extract text from {file_path}. All extraction methods failed.",
                'text': '',
                'metadata': {}
            }
            
        except Exception as e:
            logger.error(f"Error processing PDF {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_with_pymupdf(self, file_path: Path, max_pages: Optional[int] = None) -> Dict[str, Any]:
        """Extract text using PyMuPDF."""
        try:
            import fitz
            
            doc = fitz.open(file_path)
            text_parts = []
            
            num_pages = doc.page_count
            if max_pages:
                num_pages = min(num_pages, max_pages)
            
            for page_num in range(num_pages):
                page = doc[page_num]
                page_text = page.get_text()
                if page_text.strip():
                    text_parts.append(f"--- Page {page_num + 1} ---\n{page_text}")
            
            doc.close()
            
            text = '\n\n'.join(text_parts)
            
            # Extract metadata
            metadata = await self._extract_metadata_pymupdf(file_path, num_pages)
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'pymupdf',
                'extraction_method': 'pymupdf',
                'pages_processed': num_pages
            }
            
        except Exception as e:
            logger.error(f"Error extracting with PyMuPDF: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_with_pypdf2(self, file_path: Path, max_pages: Optional[int] = None) -> Dict[str, Any]:
        """Extract text using PyPDF2."""
        try:
            import PyPDF2
            
            text_parts = []
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                num_pages = len(pdf_reader.pages)
                if max_pages:
                    num_pages = min(num_pages, max_pages)
                
                for page_num in range(num_pages):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_parts.append(f"--- Page {page_num + 1} ---\n{page_text}")
            
            text = '\n\n'.join(text_parts)
            
            # Extract metadata
            metadata = await self._extract_metadata_pypdf2(file_path, num_pages)
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'pypdf2',
                'extraction_method': 'pypdf2',
                'pages_processed': num_pages
            }
            
        except Exception as e:
            logger.error(f"Error extracting with PyPDF2: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_metadata_mistral(self, file_path: Path, max_pages: Optional[int] = None) -> Dict[str, Any]:
        """Extract metadata when using Mistral OCR."""
        try:
            stat = file_path.stat()
            
            metadata = {
                'title': file_path.stem,
                'author': 'Unknown',
                'file_size': stat.st_size,
                'file_extension': file_path.suffix,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'mistral_ocr'
            }
            
            if max_pages:
                metadata['max_pages_processed'] = max_pages
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting Mistral metadata: {e}")
            return {
                'title': file_path.stem,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    async def _extract_metadata_pymupdf(self, file_path: Path, pages_processed: int) -> Dict[str, Any]:
        """Extract metadata using PyMuPDF."""
        try:
            import fitz
            
            doc = fitz.open(file_path)
            doc_metadata = doc.metadata
            
            metadata = {
                'title': doc_metadata.get('title', file_path.stem),
                'author': doc_metadata.get('author', 'Unknown'),
                'subject': doc_metadata.get('subject', ''),
                'keywords': doc_metadata.get('keywords', ''),
                'creator': doc_metadata.get('creator', ''),
                'producer': doc_metadata.get('producer', ''),
                'created': doc_metadata.get('creationDate', ''),
                'modified': doc_metadata.get('modDate', ''),
                'total_pages': doc.page_count,
                'pages_processed': pages_processed,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'pymupdf'
            }
            
            doc.close()
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting PyMuPDF metadata: {e}")
            return {
                'title': file_path.stem,
                'pages_processed': pages_processed,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    async def _extract_metadata_pypdf2(self, file_path: Path, pages_processed: int) -> Dict[str, Any]:
        """Extract metadata using PyPDF2."""
        try:
            import PyPDF2
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                doc_info = pdf_reader.metadata if pdf_reader.metadata else {}
                
                metadata = {
                    'title': doc_info.get('/Title', file_path.stem),
                    'author': doc_info.get('/Author', 'Unknown'),
                    'subject': doc_info.get('/Subject', ''),
                    'keywords': doc_info.get('/Keywords', ''),
                    'creator': doc_info.get('/Creator', ''),
                    'producer': doc_info.get('/Producer', ''),
                    'created': str(doc_info.get('/CreationDate', '')),
                    'modified': str(doc_info.get('/ModDate', '')),
                    'total_pages': len(pdf_reader.pages),
                    'pages_processed': pages_processed,
                    'file_size': file_path.stat().st_size,
                    'file_extension': file_path.suffix,
                    'extraction_timestamp': datetime.now().isoformat(),
                    'extraction_method': 'pypdf2'
                }
                
                return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting PyPDF2 metadata: {e}")
            return {
                'title': file_path.stem,
                'pages_processed': pages_processed,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if the file is supported by this processor."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return self.supported_extensions.copy()

    async def process_file(self, file_path: str, chunk_size: int = 1200, overlap: int = 0) -> Dict[str, Any]:
        """
        Process a PDF file and create episode/fact nodes in the knowledge graph.

        Args:
            file_path: Path to the PDF file
            chunk_size: Size of text chunks
            overlap: Overlap between chunks

        Returns:
            Processing result with episode_id and chunks count
        """
        try:
            from database.database_service import get_falkordb_adapter, create_episode_node, create_fact_node
            from utils.text_utils import split_text_recursively
            import uuid

            file_path_obj = Path(file_path)

            # Extract text from PDF
            extraction_result = await self.extract_text(file_path_obj)

            if not extraction_result['success']:
                return {
                    "success": False,
                    "error": f"Failed to extract text: {extraction_result.get('error', 'Unknown error')}",
                    "file_path": file_path
                }

            text = extraction_result['text']
            metadata = extraction_result.get('metadata', {})

            if not text or len(text.strip()) < 10:
                return {
                    "success": False,
                    "error": "No meaningful text extracted from PDF",
                    "file_path": file_path
                }

            # Create episode node
            episode_title = f"Document: {file_path_obj.stem}"
            episode_properties = {
                'uuid': str(uuid.uuid4()),
                'source_file': file_path,
                'extraction_method': extraction_result.get('extraction_method', 'unknown'),
                'ocr_provider': extraction_result.get('ocr_provider', 'unknown'),
                'file_size': metadata.get('file_size', 0),
                'total_pages': metadata.get('total_pages', 0),
                'processed_at': datetime.now().isoformat()
            }

            episode_id = await create_episode_node(episode_title, episode_properties)

            if not episode_id:
                return {
                    "success": False,
                    "error": "Failed to create episode node",
                    "file_path": file_path
                }

            # Chunk the text
            chunks = split_text_recursively(text, chunk_size, overlap)

            # Create fact nodes for each chunk
            facts_created = 0
            for i, chunk in enumerate(chunks):
                if chunk.strip():
                    fact_properties = {
                        'uuid': str(uuid.uuid4()),
                        'chunk_index': i,
                        'chunk_size': len(chunk),
                        'episode_id': episode_id
                    }

                    fact_id = await create_fact_node(chunk, fact_properties, episode_id)
                    if fact_id:
                        facts_created += 1

            return {
                "success": True,
                "episode_id": episode_id,
                "chunks": facts_created,
                "text_length": len(text),
                "extraction_method": extraction_result.get('extraction_method', 'unknown'),
                "file_path": file_path
            }

        except Exception as e:
            logger.error(f"Error processing PDF file {file_path}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the PDF content.
        
        Args:
            file_path: Path to the PDF
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary containing preview information
        """
        try:
            # Extract only first page for preview
            result = await self.extract_text(file_path, max_pages=1)
            
            if not result['success']:
                return result
            
            text = result['text']
            preview_text = text[:max_chars]
            
            if len(text) > max_chars:
                preview_text += "... [truncated]"
            
            return {
                'success': True,
                'preview_text': preview_text,
                'full_length': len(text),
                'metadata': result['metadata'],
                'extraction_method': result.get('extraction_method', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error generating preview for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'full_length': 0
            }
